use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{Mute<PERSON>, Semaphore};
use tracing::{debug, info, warn, error};
use kalosm::language::*;

/// Kalosm-compatible ingredient structure for structured generation
#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize)]
pub struct KalosmIngredient {
    /// The ingredient name (e.g., "all-purpose flour", "chicken breast")
    pub name: String,

    /// The amount as a decimal number (e.g., 2.5, 0.25, 1.0)
    pub amount: f64,

    /// The unit of measurement (e.g., "cup", "tbsp", "lb", "oz", "unit", "")
    pub unit: String,

    /// Optional section for grouped ingredients (e.g., "Cake Layer", "Frosting")
    pub section: Option<String>,
}

/// Performance metrics for monitoring parsing operations
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ParsingMetrics {
    pub total_requests: u64,
    pub successful_parses: u64,
    pub fallback_uses: u64,
    pub average_latency_ms: f64,
    pub model_load_time_ms: Option<u64>,
}

impl Default for ParsingMetrics {
    fn default() -> Self {
        Self {
            total_requests: 0,
            successful_parses: 0,
            fallback_uses: 0,
            average_latency_ms: 0.0,
            model_load_time_ms: None,
        }
    }
}

/// Ingredient parsing service using Kalosm for local AI inference
pub struct IngredientParser {
    /// The Kalosm language model for structured generation
    model: Arc<Mutex<Option<Llama>>>,
    /// Semaphore to limit concurrent parsing operations
    parsing_semaphore: Arc<Semaphore>,
    /// Performance metrics
    metrics: Arc<Mutex<ParsingMetrics>>,
    /// Model warming flag
    model_warmed: Arc<Mutex<bool>>,
    /// Model initialization lock to prevent concurrent loading
    model_init_lock: Arc<Mutex<()>>,
}

impl IngredientParser {
    /// Create a new ingredient parser instance
    pub fn new() -> Self {
        // Log hardware acceleration capabilities
        crate::kalosm_config::KalosmConfig::log_hardware_acceleration_info();

        Self {
            model: Arc::new(Mutex::new(None)),
            // Limit concurrent parsing to 3 operations to manage memory usage
            parsing_semaphore: Arc::new(Semaphore::new(3)),
            metrics: Arc::new(Mutex::new(ParsingMetrics::default())),
            model_warmed: Arc::new(Mutex::new(false)),
            model_init_lock: Arc::new(Mutex::new(())),
        }
    }

    /// Get current performance metrics
    pub async fn get_metrics(&self) -> ParsingMetrics {
        self.metrics.lock().await.clone()
    }

    /// Warm up the model by loading it and running a test inference
    pub async fn warm_model(&self) -> Result<()> {
        let mut warmed = self.model_warmed.lock().await;
        if *warmed {
            return Ok(());
        }

        info!("Warming up Kalosm model with hardware acceleration...");
        let start_time = Instant::now();

        // Load the model
        self.ensure_model_loaded().await?;

        // Run a simple test inference to warm up the model
        let test_result = self.kalosm_parse("1 cup flour", None).await;
        match test_result {
            Ok(_) => {
                let warm_time = start_time.elapsed();
                info!("Model warmed up successfully in {}ms with optimizations enabled", warm_time.as_millis());

                // Update metrics
                let mut metrics = self.metrics.lock().await;
                metrics.model_load_time_ms = Some(warm_time.as_millis() as u64);

                *warmed = true;
                Ok(())
            }
            Err(e) => {
                warn!("Model warming failed: {}", e);
                Err(e)
            }
        }
    }

    /// Initialize the Kalosm model (lazy loading) with optimized settings
    async fn ensure_model_loaded(&self) -> Result<()> {
        // Use a separate lock to prevent concurrent model loading
        let _init_guard = self.model_init_lock.lock().await;

        let mut model_guard = self.model.lock().await;

        if model_guard.is_none() {
            info!("Loading Kalosm Phi-3 model for ingredient parsing with hardware acceleration...");
            let load_start = Instant::now();

            // Use Phi-3 model which is lightweight and good for structured generation
            // This is a good balance between size (~2.4GB) and performance
            // The model will automatically use hardware acceleration (Metal/CUDA/MKL) if available
            match Llama::phi_3().await {
                Ok(model) => {
                    let load_time = load_start.elapsed();
                    info!("Successfully loaded Kalosm Phi-3 model with optimizations in {}ms", load_time.as_millis());

                    // Update metrics with model load time
                    {
                        let mut metrics = self.metrics.lock().await;
                        metrics.model_load_time_ms = Some(load_time.as_millis() as u64);
                    }

                    *model_guard = Some(model);
                }
                Err(e) => {
                    error!("Failed to load Kalosm model: {}", e);
                    return Err(e.into());
                }
            }
        }

        Ok(())
    }

    /// Parse a single ingredient string using Kalosm structured generation
    pub async fn parse_ingredient(
        &self,
        ingredient_text: &str,
        section: Option<String>,
    ) -> Result<Option<crate::database::Ingredient>> {
        // Skip empty or invalid ingredients
        if ingredient_text.trim().is_empty() {
            return Ok(None);
        }

        // Acquire semaphore permit to limit concurrent operations
        let _permit = self.parsing_semaphore.acquire().await.unwrap();

        let start_time = Instant::now();
        debug!("Parsing ingredient with Kalosm: '{}'", ingredient_text);

        // Update metrics
        {
            let mut metrics = self.metrics.lock().await;
            metrics.total_requests += 1;
        }

        // Try Kalosm parsing first
        let result = match self.kalosm_parse(ingredient_text, section.clone()).await {
            Ok(Some(ingredient)) => {
                debug!("Successfully parsed with Kalosm: {:?}", ingredient);

                // Update success metrics
                {
                    let mut metrics = self.metrics.lock().await;
                    metrics.successful_parses += 1;
                }

                Ok(Some(ingredient))
            }
            Ok(None) => {
                debug!("Kalosm returned None for ingredient: '{}'", ingredient_text);
                Ok(None)
            }
            Err(e) => {
                warn!("Kalosm parsing failed for '{}': {}, using fallback", ingredient_text, e);

                // Update fallback metrics
                {
                    let mut metrics = self.metrics.lock().await;
                    metrics.fallback_uses += 1;
                }

                // Fallback to regex parsing
                self.fallback_parse(ingredient_text, section)
            }
        };

        // Update latency metrics
        let elapsed = start_time.elapsed();
        {
            let mut metrics = self.metrics.lock().await;
            let total_time = metrics.average_latency_ms * (metrics.total_requests - 1) as f64;
            metrics.average_latency_ms = (total_time + elapsed.as_millis() as f64) / metrics.total_requests as f64;
        }

        result
    }

    /// Parse ingredient using Kalosm text generation
    async fn kalosm_parse(
        &self,
        ingredient_text: &str,
        section: Option<String>,
    ) -> Result<Option<crate::database::Ingredient>> {
        // Ensure model is loaded
        self.ensure_model_loaded().await?;

        let model_guard = self.model.lock().await;
        let model = model_guard.as_ref().ok_or_else(|| {
            anyhow::anyhow!("Model not loaded")
        })?;

        // Create an improved prompt for ingredient parsing
        let prompt = format!(
            r#"Parse the following ingredient into JSON format with fields: name (string), amount (number), unit (string).

IMPORTANT RULES:
1. Extract ONLY the actual food ingredient name, NOT preparation methods or cooking instructions
2. Replace underscores with spaces in ingredient names (e.g., "baking_powder" becomes "baking powder")
3. Do NOT correct or change ingredient names - preserve the original spelling and meaning
4. If the text contains only preparation methods like "chopped", "beaten", "sliced", return null
5. If no amount is specified, use 1.0
6. If no unit is specified, use "unit"
7. Clean up ingredient names (remove extra descriptive text about preparation)

Examples:
- "2 cups all-purpose flour" → {{"name": "all-purpose flour", "amount": 2.0, "unit": "cup"}}
- "chicken breast, boneless and skinless" → {{"name": "chicken breast", "amount": 1.0, "unit": "unit"}}
- "baking_powder" → {{"name": "baking powder", "amount": 1.0, "unit": "unit"}}
- "deli_ham" → {{"name": "deli ham", "amount": 1.0, "unit": "unit"}}
- ", chopped" → null (preparation method only)
- ", beaten" → null (preparation method only)

Ingredient: "{}"

Return only valid JSON or null:"#,
            ingredient_text
        );

        debug!("Kalosm prompt: {}", prompt);

        // Use task-based generation for better consistency
        let task = model.task("You are an expert ingredient parser. Extract ONLY actual food ingredients, NOT preparation methods or cooking instructions. If the text is only a preparation method like 'chopped', 'sliced', 'beaten', return null. Use proper spacing and clean ingredient names. Return valid JSON or null.");

        match task(&prompt).await {
            Ok(response) => {
                debug!("Kalosm response: {}", response);

                // Try to parse the JSON response
                match self.parse_kalosm_response(&response, section) {
                    Ok(Some(ingredient)) => Ok(Some(ingredient)),
                    Ok(None) => {
                        debug!("Failed to parse Kalosm JSON response");
                        Err(anyhow::anyhow!("Invalid JSON response from Kalosm"))
                    }
                    Err(e) => {
                        debug!("Error parsing Kalosm response: {}", e);
                        Err(e)
                    }
                }
            }
            Err(e) => {
                debug!("Kalosm text generation failed: {}", e);
                Err(e.into())
            }
        }
    }

    /// Parse the JSON response from Kalosm
    fn parse_kalosm_response(
        &self,
        response: &str,
        section: Option<String>,
    ) -> Result<Option<crate::database::Ingredient>> {
        // Clean up the response - remove any markdown formatting or extra text
        let json_str = response
            .trim()
            .trim_start_matches("```json")
            .trim_start_matches("```")
            .trim_end_matches("```")
            .trim();

        // Check if response is null or indicates no ingredient
        if json_str.eq_ignore_ascii_case("null") || json_str.is_empty() {
            debug!("Kalosm returned null - likely preparation method only");
            return Ok(None);
        }

        // Try to parse as JSON
        match serde_json::from_str::<KalosmIngredient>(json_str) {
            Ok(kalosm_ingredient) => {
                // Clean and validate the ingredient name
                let cleaned_name = self.clean_ingredient_name(&kalosm_ingredient.name);

                // Validate the parsed ingredient
                if cleaned_name.is_empty() || self.is_preparation_method(&cleaned_name) {
                    debug!("Kalosm returned invalid ingredient name: '{}'", cleaned_name);
                    return Ok(None);
                }

                // Convert to database ingredient
                let db_ingredient = crate::database::Ingredient {
                    name: cleaned_name,
                    amount: kalosm_ingredient.amount.to_string(),
                    unit: normalize_unit(&kalosm_ingredient.unit),
                    category: None,
                    section: section.or(kalosm_ingredient.section),
                };

                Ok(Some(db_ingredient))
            }
            Err(e) => {
                debug!("Failed to parse JSON response: {} - Response: {}", e, json_str);
                Err(anyhow::anyhow!("Invalid JSON format: {}", e))
            }
        }
    }

    /// Fallback to existing regex-based parsing
    fn fallback_parse(
        &self,
        ingredient_text: &str,
        section: Option<String>,
    ) -> Result<Option<crate::database::Ingredient>> {
        // Simple parsing logic for now - can be enhanced later
        let trimmed = ingredient_text.trim();

        if trimmed.is_empty() {
            return Ok(None);
        }

        // Clean the ingredient name first
        let cleaned_name = self.clean_ingredient_name(trimmed);

        // Check if it's a preparation method
        if self.is_preparation_method(&cleaned_name) {
            debug!("Fallback parser detected preparation method: '{}'", cleaned_name);
            return Ok(None);
        }

        // Basic regex pattern to extract amount, unit, and name
        let pattern = r"^(\d+(?:\.\d+)?(?:\s*[¼½¾⅓⅔⅛⅜⅝⅞])?)\s*([a-zA-Z]+)?\s*(.+)$";

        if let Ok(regex) = regex::Regex::new(pattern) {
            if let Some(captures) = regex.captures(&cleaned_name) {
                let amount_str = captures.get(1).map(|m| m.as_str()).unwrap_or("1");
                let unit = captures.get(2).map(|m| m.as_str()).unwrap_or("unit");
                let name = captures.get(3).map(|m| m.as_str()).unwrap_or(&cleaned_name);

                let amount = amount_str.parse::<f64>().unwrap_or(1.0);
                let final_name = self.clean_ingredient_name(name);

                // Final validation
                if final_name.is_empty() || self.is_preparation_method(&final_name) {
                    return Ok(None);
                }

                return Ok(Some(crate::database::Ingredient {
                    name: final_name,
                    amount: amount.to_string(),
                    unit: normalize_unit(unit),
                    category: None,
                    section,
                }));
            }
        }

        // Final validation before fallback
        if cleaned_name.is_empty() || self.is_preparation_method(&cleaned_name) {
            return Ok(None);
        }

        // Fallback: treat as ingredient name with amount 1
        Ok(Some(crate::database::Ingredient {
            name: cleaned_name,
            amount: "1".to_string(),
            unit: "unit".to_string(),
            category: None,
            section,
        }))
    }

    /// Parse multiple ingredients in batch with optimized parallel processing
    pub async fn parse_ingredients_batch(
        &self,
        ingredients: &[String],
    ) -> Result<Vec<crate::database::Ingredient>> {
        if ingredients.is_empty() {
            return Ok(Vec::new());
        }

        info!("Starting batch parsing of {} ingredients", ingredients.len());
        let start_time = Instant::now();

        // For now, process sequentially to avoid lifetime issues
        // TODO: Implement proper parallel processing with owned data
        let mut parsed_ingredients = Vec::new();

        for ingredient_str in ingredients {
            // Check if ingredient has section information (format: [Section Name] ingredient text)
            let (section, ingredient_text) = if let Some(captures) =
                regex::Regex::new(r"^\[([^\]]+)\]\s*(.+)$")
                    .unwrap()
                    .captures(ingredient_str)
            {
                (Some(captures[1].to_string()), captures[2].to_string())
            } else {
                (None, ingredient_str.clone())
            };

            if let Ok(Some(ingredient)) = self.parse_ingredient(&ingredient_text, section).await {
                parsed_ingredients.push(ingredient);
            }
        }

        let elapsed = start_time.elapsed();
        info!(
            "Batch parsing completed: {}/{} ingredients parsed in {}ms (avg: {}ms per ingredient)",
            parsed_ingredients.len(),
            ingredients.len(),
            elapsed.as_millis(),
            elapsed.as_millis() as f64 / ingredients.len() as f64
        );

        Ok(parsed_ingredients)
    }

    /// Parse ingredients in optimized batches for better memory management
    pub async fn parse_ingredients_chunked(
        &self,
        ingredients: &[String],
        chunk_size: usize,
    ) -> Result<Vec<crate::database::Ingredient>> {
        let mut all_parsed = Vec::new();

        for chunk in ingredients.chunks(chunk_size) {
            let chunk_results = self.parse_ingredients_batch(chunk).await?;
            all_parsed.extend(chunk_results);

            // Small delay between chunks to prevent overwhelming the system
            tokio::time::sleep(Duration::from_millis(10)).await;
        }

        Ok(all_parsed)
    }

    /// Clean ingredient name by fixing common issues
    fn clean_ingredient_name(&self, name: &str) -> String {
        let cleaned = name.trim();

        // Replace underscores with spaces
        let cleaned = cleaned.replace('_', " ");

        // Remove leading commas and whitespace (common parsing artifacts)
        let cleaned = cleaned.trim_start_matches(',').trim();

        // Remove preparation instructions in parentheses or after commas
        let cleaned = if let Some(comma_pos) = cleaned.find(',') {
            let before_comma = &cleaned[..comma_pos].trim();
            // Only keep the part before comma if it looks like an ingredient
            if self.looks_like_ingredient(before_comma) {
                before_comma.to_string()
            } else {
                cleaned.to_string()
            }
        } else {
            cleaned.to_string()
        };

        // Remove parenthetical preparation instructions
        let cleaned = if let Some(paren_pos) = cleaned.find('(') {
            cleaned[..paren_pos].trim().to_string()
        } else {
            cleaned
        };

        // Normalize multiple spaces to single spaces
        let cleaned = regex::Regex::new(r"\s+").unwrap().replace_all(&cleaned, " ");

        cleaned.trim().to_string()
    }

    /// Check if a string represents a preparation method rather than an ingredient
    fn is_preparation_method(&self, text: &str) -> bool {
        let text_lower = text.to_lowercase();
        let trimmed_text = text.trim();

        // Common preparation methods that should be filtered out
        let preparation_methods = [
            "beaten", "chopped", "sliced", "diced", "minced", "grated", "shredded",
            "peeled", "cut", "trimmed", "cleaned", "washed", "dried", "cooked",
            "boiled", "fried", "baked", "roasted", "grilled", "steamed",
            "or more to taste", "to taste", "as needed", "or as needed",
            "divided", "reserved", "optional", "for serving", "for garnish",
            "thawed", "defrosted", "room temperature", "softened", "melted",
            "pounded", "flattened", "butterflied", "scored", "pierced",
        ];

        // Check if the text is primarily a preparation method
        for method in &preparation_methods {
            if text_lower.contains(method) {
                // If the text is mostly the preparation method (with some descriptive words)
                // or if it's a short text containing the method, it's likely a preparation method
                let method_ratio = method.len() as f64 / text_lower.len() as f64;
                if method_ratio > 0.3 || text_lower.len() < method.len() + 15 {
                    return true;
                }
            }
        }

        // Check for very short strings that are likely parsing artifacts
        if text.len() <= 2 {
            return true;
        }

        // Check if it starts with a comma and is mostly preparation text
        if trimmed_text.starts_with(',') {
            let without_comma = trimmed_text.trim_start_matches(',').trim();
            // If after removing comma it's mostly preparation methods, filter it out
            for method in &preparation_methods {
                if without_comma.to_lowercase().contains(method) {
                    return true;
                }
            }
            // Also filter out if it's just punctuation/short text after comma
            if without_comma.len() <= 3 || !without_comma.chars().any(char::is_alphabetic) {
                return true;
            }
        }

        false
    }

    /// Check if a string looks like a valid ingredient name
    fn looks_like_ingredient(&self, text: &str) -> bool {
        let text = text.trim();

        // Must have some alphabetic characters
        if !text.chars().any(char::is_alphabetic) {
            return false;
        }

        // Must be longer than 2 characters
        if text.len() <= 2 {
            return false;
        }

        // Should not be primarily preparation methods
        !self.is_preparation_method(text)
    }
}

/// Normalize unit names to standard abbreviations
fn normalize_unit(unit: &str) -> String {
    match unit.to_lowercase().as_str() {
        "cup" | "cups" => "cup".to_string(),
        "tablespoon" | "tablespoons" | "tbsp" => "tbsp".to_string(),
        "teaspoon" | "teaspoons" | "tsp" => "tsp".to_string(),
        "pound" | "pounds" | "lb" => "lb".to_string(),
        "ounce" | "ounces" | "oz" => "oz".to_string(),
        "gram" | "grams" | "g" => "g".to_string(),
        "kilogram" | "kilograms" | "kg" => "kg".to_string(),
        "milliliter" | "milliliters" | "ml" => "ml".to_string(),
        "liter" | "liters" | "l" => "l".to_string(),
        "can" | "cans" => "can".to_string(),
        "package" | "packages" => "package".to_string(),
        "jar" | "jars" => "jar".to_string(),
        "bottle" | "bottles" => "bottle".to_string(),
        "bag" | "bags" => "bag".to_string(),
        "box" | "boxes" => "box".to_string(),
        "piece" | "pieces" => "piece".to_string(),
        "slice" | "slices" => "slice".to_string(),
        "clove" | "cloves" => "clove".to_string(),
        "stalk" | "stalks" => "stalk".to_string(),
        "" => "".to_string(),
        _ => "unit".to_string(),
    }
}

/// Global instance of the ingredient parser
static INGREDIENT_PARSER: std::sync::OnceLock<IngredientParser> = std::sync::OnceLock::new();

/// Get the global ingredient parser instance
pub fn get_ingredient_parser() -> &'static IngredientParser {
    INGREDIENT_PARSER.get_or_init(|| IngredientParser::new())
}

#[cfg(test)]
mod tests;

#[cfg(test)]
mod performance_tests;

#[cfg(test)]
mod integration_tests;
