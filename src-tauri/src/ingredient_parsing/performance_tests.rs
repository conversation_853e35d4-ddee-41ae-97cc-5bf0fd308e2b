#[cfg(test)]
mod performance_tests {
    use super::*;
    use crate::ingredient_parsing::IngredientParser;
    use std::time::{Duration, Instant};

    /// Test data representing typical recipe ingredients
    fn get_test_ingredients() -> Vec<String> {
        vec![
            "2 cups all-purpose flour".to_string(),
            "1 cup white sugar".to_string(),
            "1/2 cup unsalted butter, softened".to_string(),
            "2 large eggs".to_string(),
            "1 teaspoon vanilla extract".to_string(),
            "1/2 teaspoon salt".to_string(),
            "1 tablespoon baking powder".to_string(),
            "1 cup whole milk".to_string(),
            "1/4 cup vegetable oil".to_string(),
            "1 pound ground beef".to_string(),
            "1 medium onion, chopped".to_string(),
            "2 cloves garlic, minced".to_string(),
            "1 (14.5 ounce) can diced tomatoes".to_string(),
            "2 tablespoons tomato paste".to_string(),
            "1 teaspoon dried oregano".to_string(),
            "1/2 teaspoon ground black pepper".to_string(),
            "1 bay leaf".to_string(),
            "2 cups chicken broth".to_string(),
            "1 cup frozen peas".to_string(),
            "1/2 cup grated Parmesan cheese".to_string(),
        ]
    }

    /// Test data for large batch processing
    fn get_large_batch_ingredients() -> Vec<String> {
        let base_ingredients = get_test_ingredients();
        let mut large_batch = Vec::new();

        // Create a larger dataset by repeating and varying ingredients
        for i in 0..10 {
            for ingredient in &base_ingredients {
                large_batch.push(format!("{} (batch {})", ingredient, i));
            }
        }

        large_batch
    }

    /// Performance benchmark for individual ingredient parsing
    #[tokio::test]
    async fn test_individual_parsing_performance() {
        let parser = IngredientParser::new();
        
        let test_ingredients = vec![
            "2 cups all-purpose flour",
            "1 tablespoon olive oil",
            "3 large eggs",
            "1/2 cup sugar",
            "1 teaspoon vanilla extract",
        ];

        let start_time = Instant::now();
        let mut successful_parses = 0;

        for ingredient in &test_ingredients {
            if let Ok(Some(_)) = parser.parse_ingredient(ingredient, None).await {
                successful_parses += 1;
            }
        }

        let elapsed = start_time.elapsed();
        let avg_time_per_ingredient = elapsed.as_millis() as f64 / test_ingredients.len() as f64;

        println!("Individual parsing performance:");
        println!("  Total time: {}ms", elapsed.as_millis());
        println!("  Average per ingredient: {:.2}ms", avg_time_per_ingredient);
        println!("  Successful parses: {}/{}", successful_parses, test_ingredients.len());

        // Performance targets from KALOSM-INTEGRATION.md
        // Individual Parsing: < 500ms per ingredient
        assert!(avg_time_per_ingredient < 500.0, 
            "Average parsing time {:.2}ms exceeds target of 500ms", avg_time_per_ingredient);
    }

    /// Performance benchmark for batch ingredient parsing
    #[tokio::test]
    async fn test_batch_parsing_performance() {
        let parser = IngredientParser::new();
        
        let test_ingredients = vec![
            "2 cups all-purpose flour".to_string(),
            "1 tablespoon olive oil".to_string(),
            "3 large eggs".to_string(),
            "1/2 cup sugar".to_string(),
            "1 teaspoon vanilla extract".to_string(),
            "1 cup milk".to_string(),
            "2 teaspoons baking powder".to_string(),
            "1/2 teaspoon salt".to_string(),
            "1/4 cup butter, melted".to_string(),
            "1 teaspoon cinnamon".to_string(),
        ];

        let start_time = Instant::now();
        let results = parser.parse_ingredients_batch(&test_ingredients).await;
        let elapsed = start_time.elapsed();

        let avg_time_per_ingredient = elapsed.as_millis() as f64 / test_ingredients.len() as f64;

        println!("Batch parsing performance:");
        println!("  Total time: {}ms", elapsed.as_millis());
        println!("  Average per ingredient: {:.2}ms", avg_time_per_ingredient);
        println!("  Successful parses: {}/{}", results.unwrap_or_default().len(), test_ingredients.len());

        // Performance targets from KALOSM-INTEGRATION.md
        // Batch Parsing: < 50ms per ingredient in batches
        assert!(avg_time_per_ingredient < 50.0, 
            "Average batch parsing time {:.2}ms exceeds target of 50ms", avg_time_per_ingredient);
    }

    /// Performance benchmark for chunked batch parsing
    #[tokio::test]
    async fn test_chunked_parsing_performance() {
        let parser = IngredientParser::new();
        
        // Create a larger dataset for chunked parsing
        let mut test_ingredients = Vec::new();
        let base_ingredients = vec![
            "2 cups all-purpose flour",
            "1 tablespoon olive oil", 
            "3 large eggs",
            "1/2 cup sugar",
            "1 teaspoon vanilla extract",
        ];

        // Repeat to create 25 ingredients (5 chunks of 5)
        for i in 0..5 {
            for ingredient in &base_ingredients {
                test_ingredients.push(format!("{} (batch {})", ingredient, i + 1));
            }
        }

        let start_time = Instant::now();
        let results = parser.parse_ingredients_chunked(&test_ingredients, 5).await;
        let elapsed = start_time.elapsed();

        let avg_time_per_ingredient = elapsed.as_millis() as f64 / test_ingredients.len() as f64;

        println!("Chunked parsing performance:");
        println!("  Total time: {}ms", elapsed.as_millis());
        println!("  Average per ingredient: {:.2}ms", avg_time_per_ingredient);
        println!("  Successful parses: {}/{}", results.unwrap_or_default().len(), test_ingredients.len());

        // Should be similar to batch parsing performance
        assert!(avg_time_per_ingredient < 100.0, 
            "Average chunked parsing time {:.2}ms exceeds reasonable threshold", avg_time_per_ingredient);
    }

    /// Test concurrent parsing performance with semaphore limiting
    #[tokio::test]
    async fn test_concurrent_parsing_performance() {
        let parser = IngredientParser::new();
        
        let test_ingredients = vec![
            "2 cups flour", "1 tbsp oil", "3 eggs", "1/2 cup sugar", "1 tsp vanilla",
            "1 cup milk", "2 tsp baking powder", "1/2 tsp salt", "1/4 cup butter", "1 tsp cinnamon",
        ];

        let start_time = Instant::now();
        
        // Create concurrent tasks
        let mut tasks = Vec::new();
        for ingredient in &test_ingredients {
            let task = parser.parse_ingredient(ingredient, None);
            tasks.push(task);
        }

        // Execute all tasks concurrently
        let results = futures::future::join_all(tasks).await;
        let elapsed = start_time.elapsed();

        let successful_parses = results.iter().filter(|r| r.is_ok()).count();
        let avg_time_per_ingredient = elapsed.as_millis() as f64 / test_ingredients.len() as f64;

        println!("Concurrent parsing performance:");
        println!("  Total time: {}ms", elapsed.as_millis());
        println!("  Average per ingredient: {:.2}ms", avg_time_per_ingredient);
        println!("  Successful parses: {}/{}", successful_parses, test_ingredients.len());

        // Concurrent parsing should be faster than sequential
        assert!(elapsed.as_millis() < 1000, 
            "Concurrent parsing took {}ms, should be under 1000ms", elapsed.as_millis());
    }

    /// Test memory usage patterns during parsing
    #[tokio::test]
    async fn test_memory_usage_patterns() {
        let parser = IngredientParser::new();
        
        // Get initial metrics
        let initial_metrics = parser.get_metrics().await;
        
        // Parse a batch of ingredients
        let test_ingredients = vec![
            "2 cups flour".to_string(),
            "1 tbsp oil".to_string(), 
            "3 eggs".to_string(),
            "1/2 cup sugar".to_string(),
            "1 tsp vanilla".to_string(),
        ];

        let _results = parser.parse_ingredients_batch(&test_ingredients).await;
        
        // Get final metrics
        let final_metrics = parser.get_metrics().await;
        
        println!("Memory usage metrics:");
        println!("  Initial requests: {}", initial_metrics.total_requests);
        println!("  Final requests: {}", final_metrics.total_requests);
        println!("  Successful parses: {}", final_metrics.successful_parses);
        println!("  Fallback uses: {}", final_metrics.fallback_uses);
        println!("  Average latency: {:.2}ms", final_metrics.average_latency_ms);

        // Verify metrics are being tracked
        assert!(final_metrics.total_requests >= initial_metrics.total_requests);
        assert!(final_metrics.average_latency_ms >= 0.0);
    }

    /// Regression test to ensure performance doesn't degrade
    #[tokio::test]
    async fn test_performance_regression() {
        let parser = IngredientParser::new();
        
        let test_ingredients = vec![
            "2 cups all-purpose flour".to_string(),
            "1 tablespoon olive oil".to_string(),
            "3 large eggs".to_string(),
        ];

        // Run multiple iterations to get stable measurements
        let mut total_time = Duration::new(0, 0);
        let iterations = 3;

        for _ in 0..iterations {
            let start_time = Instant::now();
            let _results = parser.parse_ingredients_batch(&test_ingredients).await;
            total_time += start_time.elapsed();
        }

        let avg_total_time = total_time / iterations as u32;
        let avg_time_per_ingredient = avg_total_time.as_millis() as f64 / test_ingredients.len() as f64;

        println!("Performance regression test:");
        println!("  Average total time: {}ms", avg_total_time.as_millis());
        println!("  Average per ingredient: {:.2}ms", avg_time_per_ingredient);

        // Regression threshold - should not exceed 100ms per ingredient on average
        assert!(avg_time_per_ingredient < 100.0,
            "Performance regression detected: {:.2}ms per ingredient exceeds 100ms threshold",
            avg_time_per_ingredient);
    }

    /// Test sequential vs parallel parsing performance comparison
    #[tokio::test]
    async fn test_sequential_vs_parallel_performance() {
        let parser = IngredientParser::new();
        let ingredients = get_test_ingredients();

        // Warm up the model first
        let _ = parser.warm_model().await;

        // Test sequential parsing (simulate old behavior)
        let start_sequential = Instant::now();
        let mut sequential_results = Vec::new();
        for ingredient in &ingredients {
            if let Ok(Some(parsed)) = parser.parse_ingredient(ingredient, None).await {
                sequential_results.push(parsed);
            }
        }
        let sequential_time = start_sequential.elapsed();

        // Test parallel batch parsing (new optimized behavior)
        let start_parallel = Instant::now();
        let parallel_results = parser.parse_ingredients_batch(&ingredients).await.unwrap();
        let parallel_time = start_parallel.elapsed();

        println!("Sequential parsing: {}ms for {} ingredients",
                sequential_time.as_millis(), sequential_results.len());
        println!("Parallel parsing: {}ms for {} ingredients",
                parallel_time.as_millis(), parallel_results.len());

        // Calculate speedup ratio
        let speedup_ratio = sequential_time.as_millis() as f64 / parallel_time.as_millis() as f64;
        println!("Speedup ratio: {:.2}x", speedup_ratio);

        // Results should be similar in count
        assert_eq!(sequential_results.len(), parallel_results.len());

        // Parallel processing should show some benefit (at least 0.8x performance to account for overhead)
        assert!(speedup_ratio >= 0.8, "Parallel processing should not be significantly slower");
    }

    /// Test large batch performance with optimizations
    #[tokio::test]
    async fn test_large_batch_optimized_performance() {
        let parser = IngredientParser::new();
        let large_ingredients = get_large_batch_ingredients();

        println!("Testing large batch performance with {} ingredients", large_ingredients.len());

        // Warm up the model
        let _ = parser.warm_model().await;

        let start_time = Instant::now();
        let results = parser.parse_ingredients_batch(&large_ingredients).await.unwrap();
        let elapsed = start_time.elapsed();

        println!("Large batch parsing: {}ms for {}/{} ingredients",
                elapsed.as_millis(), results.len(), large_ingredients.len());
        println!("Average time per ingredient: {:.2}ms",
                elapsed.as_millis() as f64 / large_ingredients.len() as f64);

        // Should process at least 80% of ingredients successfully
        let success_rate = results.len() as f64 / large_ingredients.len() as f64;
        assert!(success_rate >= 0.8, "Should successfully parse at least 80% of ingredients");

        // Should maintain reasonable performance (less than 500ms per ingredient on average)
        let avg_time_per_ingredient = elapsed.as_millis() as f64 / large_ingredients.len() as f64;
        assert!(avg_time_per_ingredient < 500.0, "Average time per ingredient should be less than 500ms");
    }

    /// Test model initialization and caching performance
    #[tokio::test]
    async fn test_model_caching_performance() {
        // Test cold start performance
        let start_cold = Instant::now();
        let parser1 = IngredientParser::new();
        let _ = parser1.parse_ingredient("1 cup flour", None).await;
        let cold_start_time = start_cold.elapsed();

        // Test warm start performance (model already loaded)
        let start_warm = Instant::now();
        let _ = parser1.parse_ingredient("1 cup sugar", None).await;
        let warm_start_time = start_warm.elapsed();

        println!("Cold start time: {}ms", cold_start_time.as_millis());
        println!("Warm start time: {}ms", warm_start_time.as_millis());

        // Warm start should be significantly faster
        assert!(warm_start_time < cold_start_time, "Warm start should be faster than cold start");

        // Get metrics to verify model load time is tracked
        let metrics = parser1.get_metrics().await;
        assert!(metrics.model_load_time_ms.is_some(), "Model load time should be tracked");

        println!("Model load time: {}ms", metrics.model_load_time_ms.unwrap());
    }

    /// Test enhanced metrics collection
    #[tokio::test]
    async fn test_enhanced_metrics_collection() {
        let parser = IngredientParser::new();
        let ingredients = get_test_ingredients();

        // Get initial metrics
        let initial_metrics = parser.get_metrics().await;
        assert_eq!(initial_metrics.total_requests, 0);
        assert_eq!(initial_metrics.batch_operations, 0);

        // Perform batch parsing
        let _ = parser.parse_ingredients_batch(&ingredients).await.unwrap();

        // Check updated metrics
        let updated_metrics = parser.get_metrics().await;
        assert!(updated_metrics.total_requests > 0, "Should track total requests");
        assert!(updated_metrics.batch_operations > 0, "Should track batch operations");
        assert!(updated_metrics.average_latency_ms > 0.0, "Should track average latency");
        assert!(updated_metrics.parallel_efficiency >= 0.0, "Should track parallel efficiency");

        println!("Enhanced metrics after batch parsing: {:?}", updated_metrics);
    }
}
