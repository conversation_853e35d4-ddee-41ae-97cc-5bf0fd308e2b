use serde::{Deserialize, Serialize};
use std::sync::{Arc, Mutex};
use tracing::{info, warn};

/// Configuration options for Kalosm ingredient parsing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KalosmConfig {
    /// Whether Kalosm parsing is enabled (default: true)
    pub enabled: bool,
    
    /// Model selection (default: "auto")
    /// Options: "auto", "small", "medium", "large"
    pub model_selection: String,
    
    /// Parsing confidence threshold (0.0 - 1.0, default: 0.7)
    /// Results below this threshold will fall back to regex parsing
    pub confidence_threshold: f64,
    
    /// Performance vs accuracy trade-off (default: "balanced")
    /// Options: "fast", "balanced", "accurate"
    pub performance_mode: String,
    
    /// Maximum concurrent parsing operations (default: 3)
    pub max_concurrent_operations: usize,
    
    /// Enable model warming on startup (default: true)
    pub enable_model_warming: bool,
    
    /// Timeout for individual parsing operations in milliseconds (default: 5000)
    pub parsing_timeout_ms: u64,
    
    /// Enable fallback to regex parsing on errors (default: true)
    pub enable_fallback: bool,
    
    /// Enable performance metrics collection (default: true)
    pub enable_metrics: bool,
    
    /// Enable debug logging for parsing operations (default: false)
    pub enable_debug_logging: bool,
}

impl Default for KalosmConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            model_selection: "auto".to_string(),
            confidence_threshold: 0.7,
            performance_mode: "balanced".to_string(),
            max_concurrent_operations: 3,
            enable_model_warming: true,
            parsing_timeout_ms: 5000,
            enable_fallback: true,
            enable_metrics: true,
            enable_debug_logging: false,
        }
    }
}

impl KalosmConfig {
    /// Log the hardware acceleration capabilities available
    pub fn log_hardware_acceleration_info() {
        info!("Kalosm hardware acceleration status:");

        #[cfg(feature = "metal")]
        info!("  ✓ Metal acceleration enabled (macOS GPU)");
        #[cfg(not(feature = "metal"))]
        info!("  ✗ Metal acceleration disabled");

        #[cfg(feature = "cuda")]
        info!("  ✓ CUDA acceleration enabled (NVIDIA GPU)");
        #[cfg(not(feature = "cuda"))]
        info!("  ✗ CUDA acceleration disabled");

        #[cfg(feature = "mkl")]
        info!("  ✓ MKL acceleration enabled (Intel CPU optimizations)");
        #[cfg(not(feature = "mkl"))]
        info!("  ✗ MKL acceleration disabled");

        // Check if we're running in release mode
        if cfg!(debug_assertions) {
            warn!("Running in debug mode - performance may be slower. Use --release for optimal performance.");
        } else {
            info!("  ✓ Running in release mode with full optimizations");
        }
    }

    ///
    /// Validate configuration values
    pub fn validate(&self) -> Result<(), String> {
        if self.confidence_threshold < 0.0 || self.confidence_threshold > 1.0 {
            return Err("Confidence threshold must be between 0.0 and 1.0".to_string());
        }
        
        if self.max_concurrent_operations == 0 {
            return Err("Max concurrent operations must be greater than 0".to_string());
        }
        
        if self.max_concurrent_operations > 10 {
            return Err("Max concurrent operations should not exceed 10 for memory safety".to_string());
        }
        
        if self.parsing_timeout_ms < 100 {
            return Err("Parsing timeout must be at least 100ms".to_string());
        }
        
        if self.parsing_timeout_ms > 30000 {
            return Err("Parsing timeout should not exceed 30 seconds".to_string());
        }
        
        match self.model_selection.as_str() {
            "auto" | "small" | "medium" | "large" => {},
            _ => return Err("Model selection must be one of: auto, small, medium, large".to_string()),
        }
        
        match self.performance_mode.as_str() {
            "fast" | "balanced" | "accurate" => {},
            _ => return Err("Performance mode must be one of: fast, balanced, accurate".to_string()),
        }
        
        Ok(())
    }
    
    /// Get recommended settings based on performance mode
    pub fn apply_performance_mode(&mut self) {
        match self.performance_mode.as_str() {
            "fast" => {
                self.confidence_threshold = 0.5;
                self.max_concurrent_operations = 5;
                self.parsing_timeout_ms = 2000;
                self.enable_model_warming = false;
                info!("Applied fast performance mode settings with hardware acceleration");
            },
            "balanced" => {
                self.confidence_threshold = 0.7;
                self.max_concurrent_operations = 3;
                self.parsing_timeout_ms = 5000;
                self.enable_model_warming = true;
                info!("Applied balanced performance mode settings with hardware acceleration");
            },
            "accurate" => {
                self.confidence_threshold = 0.8;
                self.max_concurrent_operations = 2;
                self.parsing_timeout_ms = 10000;
                self.enable_model_warming = true;
                info!("Applied accurate performance mode settings with hardware acceleration");
            },
            _ => {
                warn!("Unknown performance mode: {}, using balanced", self.performance_mode);
                self.performance_mode = "balanced".to_string();
                self.apply_performance_mode();
            }
        }
    }
}

/// Global configuration manager for Kalosm settings
pub struct KalosmConfigManager {
    config: Arc<Mutex<KalosmConfig>>,
}

impl KalosmConfigManager {
    /// Create a new configuration manager with default settings
    pub fn new() -> Self {
        Self {
            config: Arc::new(Mutex::new(KalosmConfig::default())),
        }
    }
    
    /// Get the current configuration
    pub fn get_config(&self) -> KalosmConfig {
        self.config.lock().unwrap().clone()
    }
    
    /// Update the configuration
    pub fn update_config(&self, new_config: KalosmConfig) -> Result<(), String> {
        new_config.validate()?;
        
        let mut config = self.config.lock().unwrap();
        *config = new_config;
        
        info!("Kalosm configuration updated successfully");
        Ok(())
    }
    
    /// Update a specific configuration field
    pub fn update_field<F>(&self, updater: F) -> Result<(), String>
    where
        F: FnOnce(&mut KalosmConfig),
    {
        let mut config = self.config.lock().unwrap();
        updater(&mut config);
        config.validate()?;
        
        info!("Kalosm configuration field updated successfully");
        Ok(())
    }
    
    /// Reset configuration to defaults
    pub fn reset_to_defaults(&self) {
        let mut config = self.config.lock().unwrap();
        *config = KalosmConfig::default();
        
        info!("Kalosm configuration reset to defaults");
    }
    
    /// Load configuration from JSON string
    pub fn load_from_json(&self, json_str: &str) -> Result<(), String> {
        let new_config: KalosmConfig = serde_json::from_str(json_str)
            .map_err(|e| format!("Failed to parse configuration JSON: {}", e))?;
        
        self.update_config(new_config)
    }
    
    /// Save configuration to JSON string
    pub fn save_to_json(&self) -> Result<String, String> {
        let config = self.config.lock().unwrap();
        serde_json::to_string_pretty(&*config)
            .map_err(|e| format!("Failed to serialize configuration: {}", e))
    }
}

impl Default for KalosmConfigManager {
    fn default() -> Self {
        Self::new()
    }
}

// Global configuration instance
static mut GLOBAL_CONFIG: Option<KalosmConfigManager> = None;
static INIT: std::sync::Once = std::sync::Once::new();

/// Get the global Kalosm configuration manager
pub fn get_kalosm_config_manager() -> &'static KalosmConfigManager {
    unsafe {
        INIT.call_once(|| {
            GLOBAL_CONFIG = Some(KalosmConfigManager::new());
        });
        GLOBAL_CONFIG.as_ref().unwrap()
    }
}

/// Get the current Kalosm configuration
pub fn get_kalosm_config() -> KalosmConfig {
    get_kalosm_config_manager().get_config()
}

/// Update the global Kalosm configuration
pub fn update_kalosm_config(config: KalosmConfig) -> Result<(), String> {
    get_kalosm_config_manager().update_config(config)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_config() {
        let config = KalosmConfig::default();
        assert!(config.enabled);
        assert_eq!(config.model_selection, "auto");
        assert_eq!(config.confidence_threshold, 0.7);
        assert_eq!(config.performance_mode, "balanced");
        assert_eq!(config.max_concurrent_operations, 3);
        assert!(config.enable_model_warming);
        assert_eq!(config.parsing_timeout_ms, 5000);
        assert!(config.enable_fallback);
        assert!(config.enable_metrics);
        assert!(!config.enable_debug_logging);
    }

    #[test]
    fn test_config_validation() {
        let mut config = KalosmConfig::default();
        
        // Valid config should pass
        assert!(config.validate().is_ok());
        
        // Invalid confidence threshold
        config.confidence_threshold = -0.1;
        assert!(config.validate().is_err());
        
        config.confidence_threshold = 1.1;
        assert!(config.validate().is_err());
        
        // Reset and test other fields
        config = KalosmConfig::default();
        
        // Invalid max concurrent operations
        config.max_concurrent_operations = 0;
        assert!(config.validate().is_err());
        
        config.max_concurrent_operations = 15;
        assert!(config.validate().is_err());
        
        // Reset and test timeout
        config = KalosmConfig::default();
        
        config.parsing_timeout_ms = 50;
        assert!(config.validate().is_err());
        
        config.parsing_timeout_ms = 40000;
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_performance_modes() {
        let mut config = KalosmConfig::default();
        
        // Test fast mode
        config.performance_mode = "fast".to_string();
        config.apply_performance_mode();
        assert_eq!(config.confidence_threshold, 0.5);
        assert_eq!(config.max_concurrent_operations, 5);
        assert_eq!(config.parsing_timeout_ms, 2000);
        assert!(!config.enable_model_warming);
        
        // Test accurate mode
        config.performance_mode = "accurate".to_string();
        config.apply_performance_mode();
        assert_eq!(config.confidence_threshold, 0.8);
        assert_eq!(config.max_concurrent_operations, 2);
        assert_eq!(config.parsing_timeout_ms, 10000);
        assert!(config.enable_model_warming);
    }

    #[test]
    fn test_config_manager() {
        let manager = KalosmConfigManager::new();
        
        // Test getting default config
        let config = manager.get_config();
        assert!(config.enabled);
        
        // Test updating config
        let mut new_config = config.clone();
        new_config.enabled = false;
        new_config.confidence_threshold = 0.8;
        
        assert!(manager.update_config(new_config.clone()).is_ok());
        
        let updated_config = manager.get_config();
        assert!(!updated_config.enabled);
        assert_eq!(updated_config.confidence_threshold, 0.8);
        
        // Test invalid config update
        let mut invalid_config = new_config;
        invalid_config.confidence_threshold = 2.0;
        assert!(manager.update_config(invalid_config).is_err());
    }

    #[test]
    fn test_json_serialization() {
        let manager = KalosmConfigManager::new();
        
        // Test saving to JSON
        let json_str = manager.save_to_json().unwrap();
        assert!(json_str.contains("enabled"));
        assert!(json_str.contains("model_selection"));
        
        // Test loading from JSON
        let new_manager = KalosmConfigManager::new();
        assert!(new_manager.load_from_json(&json_str).is_ok());
        
        let loaded_config = new_manager.get_config();
        let original_config = manager.get_config();
        
        assert_eq!(loaded_config.enabled, original_config.enabled);
        assert_eq!(loaded_config.model_selection, original_config.model_selection);
        assert_eq!(loaded_config.confidence_threshold, original_config.confidence_threshold);
    }
}
